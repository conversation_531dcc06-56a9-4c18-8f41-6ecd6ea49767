<!DOCTYPE html>
<html>
<head>
    <title>CORS Test for Cross Request Extension</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        button { margin: 5px; padding: 10px; }
        #results { margin-top: 20px; }
    </style>
</head>
<body>
    <h1>CORS Test for Cross Request Extension</h1>
    <div id="cross-request-sign" style="display: none;"></div>
    
    <div class="test-section">
        <h3>Test 1: Same Origin Request</h3>
        <button onclick="testSameOrigin()">Test Same Origin</button>
        <div id="same-origin-result"></div>
    </div>
    
    <div class="test-section">
        <h3>Test 2: Cross Origin Request (HTTP)</h3>
        <button onclick="testCrossOriginHttp()">Test HTTP Cross Origin</button>
        <div id="cross-origin-http-result"></div>
    </div>
    
    <div class="test-section">
        <h3>Test 3: Cross Origin Request (HTTPS)</h3>
        <button onclick="testCrossOriginHttps()">Test HTTPS Cross Origin</button>
        <div id="cross-origin-https-result"></div>
    </div>
    
    <div class="test-section">
        <h3>Test 4: POST Request with JSON</h3>
        <button onclick="testPostJson()">Test POST JSON</button>
        <div id="post-json-result"></div>
    </div>
    
    <div id="results"></div>

    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const p = document.createElement('p');
            p.className = type;
            p.textContent = new Date().toLocaleTimeString() + ': ' + message;
            results.appendChild(p);
            console.log(message);
        }

        function testSameOrigin() {
            const resultDiv = document.getElementById('same-origin-result');
            resultDiv.innerHTML = '<p>Testing same origin request...</p>';
            
            if (typeof crossRequest === 'undefined') {
                resultDiv.innerHTML = '<p class="error">❌ crossRequest not available</p>';
                return;
            }
            
            crossRequest({
                url: window.location.origin + '/test',
                method: 'GET',
                success: function(res, header, data) {
                    resultDiv.innerHTML = '<p class="success">✅ Same origin request successful</p>';
                    log('Same origin request successful', 'success');
                },
                error: function(error, header, data) {
                    resultDiv.innerHTML = '<p class="warning">⚠ Same origin request failed (expected): ' + (error.body || error.statusText) + '</p>';
                    log('Same origin request failed: ' + (error.body || error.statusText), 'warning');
                }
            });
        }

        function testCrossOriginHttp() {
            const resultDiv = document.getElementById('cross-origin-http-result');
            resultDiv.innerHTML = '<p>Testing HTTP cross origin request...</p>';
            
            if (typeof crossRequest === 'undefined') {
                resultDiv.innerHTML = '<p class="error">❌ crossRequest not available</p>';
                return;
            }
            
            crossRequest({
                url: 'http://httpbin.org/get',
                method: 'GET',
                success: function(res, header, data) {
                    resultDiv.innerHTML = '<p class="success">✅ HTTP cross origin request successful</p>';
                    log('HTTP cross origin request successful', 'success');
                },
                error: function(error, header, data) {
                    resultDiv.innerHTML = '<p class="error">❌ HTTP cross origin request failed: ' + (error.body || error.statusText) + '</p>';
                    log('HTTP cross origin request failed: ' + (error.body || error.statusText), 'error');
                }
            });
        }

        function testCrossOriginHttps() {
            const resultDiv = document.getElementById('cross-origin-https-result');
            resultDiv.innerHTML = '<p>Testing HTTPS cross origin request...</p>';
            
            if (typeof crossRequest === 'undefined') {
                resultDiv.innerHTML = '<p class="error">❌ crossRequest not available</p>';
                return;
            }
            
            crossRequest({
                url: 'https://httpbin.org/get',
                method: 'GET',
                success: function(res, header, data) {
                    resultDiv.innerHTML = '<p class="success">✅ HTTPS cross origin request successful</p>';
                    log('HTTPS cross origin request successful', 'success');
                },
                error: function(error, header, data) {
                    resultDiv.innerHTML = '<p class="error">❌ HTTPS cross origin request failed: ' + (error.body || error.statusText) + '</p>';
                    log('HTTPS cross origin request failed: ' + (error.body || error.statusText), 'error');
                }
            });
        }

        function testPostJson() {
            const resultDiv = document.getElementById('post-json-result');
            resultDiv.innerHTML = '<p>Testing POST JSON request...</p>';
            
            if (typeof crossRequest === 'undefined') {
                resultDiv.innerHTML = '<p class="error">❌ crossRequest not available</p>';
                return;
            }
            
            crossRequest({
                url: 'https://httpbin.org/post',
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                data: {
                    test: 'data',
                    timestamp: Date.now()
                },
                success: function(res, header, data) {
                    resultDiv.innerHTML = '<p class="success">✅ POST JSON request successful</p>';
                    log('POST JSON request successful', 'success');
                },
                error: function(error, header, data) {
                    resultDiv.innerHTML = '<p class="error">❌ POST JSON request failed: ' + (error.body || error.statusText) + '</p>';
                    log('POST JSON request failed: ' + (error.body || error.statusText), 'error');
                }
            });
        }

        // 自动检测插件状态
        setTimeout(function() {
            if (typeof crossRequest !== 'undefined') {
                log('✅ Extension loaded successfully', 'success');
            } else {
                log('❌ Extension not loaded', 'error');
            }
        }, 1000);
    </script>
</body>
</html>
