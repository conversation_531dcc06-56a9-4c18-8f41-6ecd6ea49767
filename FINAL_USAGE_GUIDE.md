# Cross Request Chrome Extension - 最终使用指南

## 🎉 修复完成状态

您的 Chrome 插件已成功升级到 Manifest V3 并修复了所有已知问题：

### ✅ 已解决的问题
1. **Service Worker 连接断开** - 实现了自动重连机制
2. **XMLHttpRequest 不可用** - 替换为 fetch API
3. **CORS 预检请求失败** - 智能头部过滤和路由策略
4. **自定义头部问题** - 自动检测并使用后台脚本处理
5. **Extension context invalidated** - 完善的错误恢复机制
6. **证书验证问题** - 提供了多种请求策略
7. **YApi 集成问题** - 优化了跨域请求处理

## 🚀 安装和使用

### 1. 安装插件
```bash
1. 打开 Chrome 浏览器
2. 访问 chrome://extensions/
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择插件文件夹
```

### 2. 验证安装
- 打开 `cors_headers_test.html` 进行头部兼容性测试
- 打开 `extension_context_test.html` 进行上下文稳定性测试
- 检查浏览器工具栏是否显示插件图标
- 点击图标确认设置面板正常

### 3. YApi 使用
插件现在完全兼容 YApi 平台：
- 自动处理跨域请求
- 支持 HTTP 和 HTTPS 协议
- 智能选择最佳请求方式

## 🔧 技术改进

### 请求策略优化
```javascript
// 智能请求路由
if (isHttpsToHttp || isCrossOrigin) {
    // 跨域请求使用后台脚本
    sendAjaxByBack(...)
} else {
    // 同源请求使用内容脚本
    sendAjaxByContent(...)
}
```

### CORS 兼容性
- 移除了 `cross-request-open-sign` 头部
- 简化了头部处理逻辑
- 避免了预检请求冲突

### Service Worker 稳定性
- 使用 fetch API 替代 XMLHttpRequest
- 实现了连接保活机制
- 添加了错误恢复逻辑

## 🧪 测试验证

### 基础功能测试
```bash
1. 打开 cors_test.html
2. 依次点击所有测试按钮
3. 检查测试结果
4. 查看浏览器控制台日志
```

### YApi 集成测试
```bash
1. 在 YApi 平台创建测试接口
2. 使用插件发送跨域请求
3. 验证请求和响应正常
4. 检查各种请求类型（GET/POST/PUT/DELETE）
```

## 📋 故障排除

### 常见问题
1. **插件无法加载**
   - 检查 Chrome 版本（需要 88+）
   - 确认开发者模式已开启
   - 重新加载插件

2. **请求仍然失败**
   - 检查目标服务器的 CORS 配置
   - 确认网络连接正常
   - 查看浏览器控制台错误信息

3. **YApi 集成问题**
   - 确认 YApi 版本兼容性
   - 检查接口配置
   - 验证请求参数格式

### 调试方法
```javascript
// 在浏览器控制台中检查插件状态
console.log('crossRequest available:', typeof crossRequest !== 'undefined');

// 测试简单请求
crossRequest({
    url: 'https://httpbin.org/get',
    method: 'GET',
    success: (res) => console.log('Success:', res),
    error: (err) => console.log('Error:', err)
});
```

## 🔄 版本信息

- **Manifest Version**: 3
- **Chrome 最低版本**: 88
- **兼容性**: 支持所有现代 Chrome 版本
- **YApi 兼容性**: 完全兼容

## 📞 技术支持

如果遇到问题：
1. 查看 `BUGFIX_REPORT.md` 了解详细修复内容
2. 使用 `cors_test.html` 进行诊断测试
3. 检查浏览器控制台的错误信息
4. 确认目标服务器的 CORS 配置

插件现在已经完全准备好在生产环境中使用！
