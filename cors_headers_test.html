<!DOCTYPE html>
<html>
<head>
    <title>CORS Headers Test</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        button { margin: 5px; padding: 10px; }
        #results { margin-top: 20px; max-height: 400px; overflow-y: auto; }
        .header-test { background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>CORS Headers Test</h1>
    <div id="cross-request-sign" style="display: none;"></div>
    
    <div class="test-section">
        <h3>Test Custom Headers</h3>
        <p>Testing various headers that might cause CORS preflight issues:</p>
        <button onclick="testOperatorSource()">Test operatorsource Header</button>
        <button onclick="testAuthorizationHeader()">Test Authorization Header</button>
        <button onclick="testCustomXHeader()">Test X-Custom Header</button>
        <button onclick="testSafeHeaders()">Test Safe Headers Only</button>
        <button onclick="testAllHeaders()">Test All Headers</button>
    </div>
    
    <div class="test-section">
        <h3>Test Different Request Types</h3>
        <button onclick="testSimpleGET()">Simple GET</button>
        <button onclick="testJSONPOST()">JSON POST</button>
        <button onclick="testFormPOST()">Form POST</button>
        <button onclick="testCrossOrigin()">Cross-Origin Request</button>
    </div>
    
    <div id="results"></div>

    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const p = document.createElement('div');
            p.className = type;
            p.innerHTML = `<strong>[${new Date().toLocaleTimeString()}]</strong> ${message}`;
            results.appendChild(p);
            results.scrollTop = results.scrollHeight;
            console.log(message);
        }

        function testOperatorSource() {
            log('Testing request with operatorsource header...', 'info');
            
            if (typeof crossRequest === 'undefined') {
                log('❌ crossRequest not available', 'error');
                return;
            }
            
            crossRequest({
                url: 'https://httpbin.org/headers',
                method: 'GET',
                headers: {
                    'operatorsource': 'test-value',
                    'Content-Type': 'application/json'
                },
                success: function(res, header, data) {
                    log('✅ operatorsource header request successful', 'success');
                    try {
                        const responseData = JSON.parse(res.body);
                        if (responseData.headers && responseData.headers.operatorsource) {
                            log('✅ operatorsource header was sent successfully', 'success');
                        } else {
                            log('⚠ operatorsource header was filtered out', 'warning');
                        }
                    } catch (e) {
                        log('Response parsing failed', 'warning');
                    }
                },
                error: function(error, header, data) {
                    log(`❌ operatorsource header request failed: ${error.body || error.statusText}`, 'error');
                }
            });
        }

        function testAuthorizationHeader() {
            log('Testing request with Authorization header...', 'info');
            
            crossRequest({
                url: 'https://httpbin.org/headers',
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer test-token',
                    'Content-Type': 'application/json'
                },
                success: function(res, header, data) {
                    log('✅ Authorization header request successful', 'success');
                },
                error: function(error, header, data) {
                    log(`❌ Authorization header request failed: ${error.body || error.statusText}`, 'error');
                }
            });
        }

        function testCustomXHeader() {
            log('Testing request with X-Custom header...', 'info');
            
            crossRequest({
                url: 'https://httpbin.org/headers',
                method: 'GET',
                headers: {
                    'X-Custom-Header': 'custom-value',
                    'X-API-Key': 'api-key-value'
                },
                success: function(res, header, data) {
                    log('✅ Custom X headers request successful', 'success');
                },
                error: function(error, header, data) {
                    log(`❌ Custom X headers request failed: ${error.body || error.statusText}`, 'error');
                }
            });
        }

        function testSafeHeaders() {
            log('Testing request with safe headers only...', 'info');
            
            crossRequest({
                url: 'https://httpbin.org/headers',
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                success: function(res, header, data) {
                    log('✅ Safe headers request successful', 'success');
                },
                error: function(error, header, data) {
                    log(`❌ Safe headers request failed: ${error.body || error.statusText}`, 'error');
                }
            });
        }

        function testAllHeaders() {
            log('Testing request with mixed headers...', 'info');
            
            crossRequest({
                url: 'https://httpbin.org/headers',
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'operatorsource': 'yapi-test',
                    'Authorization': 'Bearer token123',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-Custom-Header': 'test-value',
                    'Accept': 'application/json'
                },
                data: {
                    test: 'data',
                    timestamp: Date.now()
                },
                success: function(res, header, data) {
                    log('✅ Mixed headers request successful', 'success');
                    try {
                        const responseData = JSON.parse(res.body);
                        log('Headers received by server: ' + JSON.stringify(responseData.headers, null, 2), 'info');
                    } catch (e) {
                        log('Could not parse response', 'warning');
                    }
                },
                error: function(error, header, data) {
                    log(`❌ Mixed headers request failed: ${error.body || error.statusText}`, 'error');
                }
            });
        }

        function testSimpleGET() {
            log('Testing simple GET request...', 'info');
            
            crossRequest({
                url: 'https://httpbin.org/get?test=simple',
                method: 'GET',
                success: function(res, header, data) {
                    log('✅ Simple GET successful', 'success');
                },
                error: function(error, header, data) {
                    log(`❌ Simple GET failed: ${error.body || error.statusText}`, 'error');
                }
            });
        }

        function testJSONPOST() {
            log('Testing JSON POST request...', 'info');
            
            crossRequest({
                url: 'https://httpbin.org/post',
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                data: {
                    message: 'JSON POST test',
                    timestamp: Date.now()
                },
                success: function(res, header, data) {
                    log('✅ JSON POST successful', 'success');
                },
                error: function(error, header, data) {
                    log(`❌ JSON POST failed: ${error.body || error.statusText}`, 'error');
                }
            });
        }

        function testFormPOST() {
            log('Testing Form POST request...', 'info');
            
            crossRequest({
                url: 'https://httpbin.org/post',
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                data: {
                    message: 'Form POST test',
                    timestamp: Date.now()
                },
                success: function(res, header, data) {
                    log('✅ Form POST successful', 'success');
                },
                error: function(error, header, data) {
                    log(`❌ Form POST failed: ${error.body || error.statusText}`, 'error');
                }
            });
        }

        function testCrossOrigin() {
            log('Testing cross-origin request to localhost...', 'info');
            
            crossRequest({
                url: 'http://localhost:8007/test',
                method: 'GET',
                headers: {
                    'operatorsource': 'yapi-extension'
                },
                success: function(res, header, data) {
                    log('✅ Cross-origin localhost request successful', 'success');
                },
                error: function(error, header, data) {
                    log(`❌ Cross-origin localhost request failed: ${error.body || error.statusText}`, 'error');
                }
            });
        }

        // 自动检测插件状态
        setTimeout(() => {
            if (typeof crossRequest !== 'undefined') {
                log('✅ Extension loaded successfully', 'success');
            } else {
                log('❌ Extension not loaded', 'error');
            }
        }, 1000);
    </script>
</body>
</html>
