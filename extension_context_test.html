<!DOCTYPE html>
<html>
<head>
    <title>Extension Context Test</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { margin: 5px; padding: 10px; }
        #log { max-height: 400px; overflow-y: auto; background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; }
    </style>
</head>
<body>
    <h1>Extension Context Test</h1>
    <div id="cross-request-sign" style="display: none;"></div>
    
    <div class="status" id="extension-status">
        <strong>Extension Status:</strong> <span id="status-text">Checking...</span>
    </div>
    
    <div>
        <button onclick="checkExtensionContext()">Check Extension Context</button>
        <button onclick="testSimpleRequest()">Test Simple Request</button>
        <button onclick="testCrossOriginRequest()">Test Cross-Origin Request</button>
        <button onclick="simulateExtensionReload()">Simulate Extension Reload</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>
    
    <h3>Test Log:</h3>
    <div id="log"></div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logEntry.className = type;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function updateStatus(message, type) {
            const statusDiv = document.getElementById('extension-status');
            const statusText = document.getElementById('status-text');
            statusText.textContent = message;
            statusDiv.className = 'status ' + type;
        }

        function checkExtensionContext() {
            log('Checking extension context...', 'info');
            
            // 检查基本的 chrome API
            if (typeof chrome === 'undefined') {
                log('❌ chrome object is undefined', 'error');
                updateStatus('Chrome APIs not available', 'error');
                return false;
            }
            
            if (!chrome.runtime) {
                log('❌ chrome.runtime is undefined', 'error');
                updateStatus('Chrome runtime not available', 'error');
                return false;
            }
            
            try {
                const extensionId = chrome.runtime.id;
                if (extensionId) {
                    log(`✅ Extension ID: ${extensionId}`, 'success');
                    updateStatus('Extension context valid', 'success');
                } else {
                    log('❌ Extension ID is undefined', 'error');
                    updateStatus('Extension ID not available', 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ Error accessing extension ID: ${error.message}`, 'error');
                updateStatus('Extension context invalid', 'error');
                return false;
            }
            
            // 检查 crossRequest 函数
            if (typeof crossRequest !== 'undefined') {
                log('✅ crossRequest function is available', 'success');
            } else {
                log('❌ crossRequest function is not available', 'error');
                updateStatus('crossRequest not loaded', 'warning');
            }
            
            return true;
        }

        function testSimpleRequest() {
            log('Testing simple request...', 'info');
            
            if (typeof crossRequest === 'undefined') {
                log('❌ crossRequest not available', 'error');
                return;
            }
            
            crossRequest({
                url: 'https://httpbin.org/get?test=simple',
                method: 'GET',
                timeout: 10000,
                success: function(res, header, data) {
                    log('✅ Simple request successful', 'success');
                    log(`Response status: ${res.status || 'unknown'}`, 'info');
                },
                error: function(error, header, data) {
                    log(`❌ Simple request failed: ${error.body || error.statusText || 'Unknown error'}`, 'error');
                }
            });
        }

        function testCrossOriginRequest() {
            log('Testing cross-origin request...', 'info');
            
            if (typeof crossRequest === 'undefined') {
                log('❌ crossRequest not available', 'error');
                return;
            }
            
            crossRequest({
                url: 'https://api.github.com/users/octocat',
                method: 'GET',
                timeout: 10000,
                success: function(res, header, data) {
                    log('✅ Cross-origin request successful', 'success');
                    log(`Response status: ${res.status || 'unknown'}`, 'info');
                },
                error: function(error, header, data) {
                    log(`❌ Cross-origin request failed: ${error.body || error.statusText || 'Unknown error'}`, 'error');
                }
            });
        }

        function simulateExtensionReload() {
            log('Simulating extension context invalidation...', 'warning');
            
            // 模拟扩展上下文失效
            if (window.chrome && window.chrome.runtime) {
                // 临时保存原始对象
                const originalRuntime = window.chrome.runtime;
                
                // 模拟上下文失效
                delete window.chrome.runtime;
                
                log('Extension context invalidated (simulated)', 'warning');
                updateStatus('Extension context invalidated', 'error');
                
                // 3秒后恢复
                setTimeout(() => {
                    window.chrome.runtime = originalRuntime;
                    log('Extension context restored (simulated)', 'success');
                    checkExtensionContext();
                }, 3000);
            }
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 自动检查扩展状态
        setTimeout(() => {
            checkExtensionContext();
        }, 1000);

        // 定期检查扩展上下文
        setInterval(() => {
            if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id) {
                // 上下文有效
                if (document.getElementById('status-text').textContent.includes('invalid')) {
                    log('Extension context recovered', 'success');
                    checkExtensionContext();
                }
            } else {
                // 上下文无效
                if (!document.getElementById('status-text').textContent.includes('invalid')) {
                    log('Extension context lost', 'error');
                    updateStatus('Extension context invalid', 'error');
                }
            }
        }, 2000);
    </script>
</body>
</html>
