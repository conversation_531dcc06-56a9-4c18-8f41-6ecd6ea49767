'use strict';

var base64 = _base64();

function formUrlencode(data) {
	return Object.keys(data).map(function (key) {
		return encodeURIComponent(key) + '=' + encodeURIComponent(data[key]);
	}).join('&')
}

function encode(data) {
	return base64.encode(encodeURIComponent(JSON.stringify(data)));
}

function decode(data) {
	return JSON.parse(decodeURIComponent(base64.decode(data)));
}


function _base64() {

	/*--------------------------------------------------------------------------*/

	var InvalidCharacterError = function (message) {
		this.message = message;
	};
	InvalidCharacterError.prototype = new Error;
	InvalidCharacterError.prototype.name = 'InvalidCharacterError';

	var error = function (message) {
		// Note: the error messages used throughout this file match those used by
		// the native `atob`/`btoa` implementation in Chromium.
		throw new InvalidCharacterError(message);
	};

	var TABLE = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
	// http://whatwg.org/html/common-microsyntaxes.html#space-character
	var REGEX_SPACE_CHARACTERS = /<%= spaceCharacters %>/g;

	// `decode` is designed to be fully compatible with `atob` as described in the
	// HTML Standard. http://whatwg.org/html/webappapis.html#dom-windowbase64-atob
	// The optimized base64-decoding algorithm used is based on @atk’s excellent
	// implementation. https://gist.github.com/atk/1020396
	var decode = function (input) {
		input = String(input)
			.replace(REGEX_SPACE_CHARACTERS, '');
		var length = input.length;
		if (length % 4 == 0) {
			input = input.replace(/==?$/, '');
			length = input.length;
		}
		if (
			length % 4 == 1 ||
			// http://whatwg.org/C#alphanumeric-ascii-characters
			/[^+a-zA-Z0-9/]/.test(input)
		) {
			error(
				'Invalid character: the string to be decoded is not correctly encoded.'
			);
		}
		var bitCounter = 0;
		var bitStorage;
		var buffer;
		var output = '';
		var position = -1;
		while (++position < length) {
			buffer = TABLE.indexOf(input.charAt(position));
			bitStorage = bitCounter % 4 ? bitStorage * 64 + buffer : buffer;
			// Unless this is the first of a group of 4 characters…
			if (bitCounter++ % 4) {
				// …convert the first 8 bits to a single ASCII character.
				output += String.fromCharCode(
					0xFF & bitStorage >> (-2 * bitCounter & 6)
				);
			}
		}
		return output;
	};

	// `encode` is designed to be fully compatible with `btoa` as described in the
	// HTML Standard: http://whatwg.org/html/webappapis.html#dom-windowbase64-btoa
	var encode = function (input) {
		input = String(input);
		if (/[^\0-\xFF]/.test(input)) {
			// Note: no need to special-case astral symbols here, as surrogates are
			// matched, and the input is supposed to only contain ASCII anyway.
			error(
				'The string to be encoded contains characters outside of the ' +
				'Latin1 range.'
			);
		}
		var padding = input.length % 3;
		var output = '';
		var position = -1;
		var a;
		var b;
		var c;
		var d;
		var buffer;
		// Make sure any padding is handled outside of the loop.
		var length = input.length - padding;

		while (++position < length) {
			// Read three bytes, i.e. 24 bits.
			a = input.charCodeAt(position) << 16;
			b = input.charCodeAt(++position) << 8;
			c = input.charCodeAt(++position);
			buffer = a + b + c;
			// Turn the 24 bits into four chunks of 6 bits each, and append the
			// matching character for each of them to the output.
			output += (
				TABLE.charAt(buffer >> 18 & 0x3F) +
				TABLE.charAt(buffer >> 12 & 0x3F) +
				TABLE.charAt(buffer >> 6 & 0x3F) +
				TABLE.charAt(buffer & 0x3F)
			);
		}

		if (padding == 2) {
			a = input.charCodeAt(position) << 8;
			b = input.charCodeAt(++position);
			buffer = a + b;
			output += (
				TABLE.charAt(buffer >> 10) +
				TABLE.charAt((buffer >> 4) & 0x3F) +
				TABLE.charAt((buffer << 2) & 0x3F) +
				'='
			);
		} else if (padding == 1) {
			buffer = input.charCodeAt(position);
			output += (
				TABLE.charAt(buffer >> 2) +
				TABLE.charAt((buffer << 4) & 0x3F) +
				'=='
			);
		}

		return output;
	};

	return {
		'encode': encode,
		'decode': decode,
		'version': '<%= version %>'
	};
};






var unsafeHeader = ['Accept-Charset',
	'Accept-Encoding',
	'Access-Control-Request-Headers',
	'Access-Control-Request-Method',
	'Connection',
	'Content-Length',
	'Cookie',
	'Cookie2',
	'Content-Transfer-Encoding',
	'Date',
	'Expect',
	'Host',
	'Keep-Alive',
	'Origin',
	'Referer',
	'TE',
	'Trailer',
	'Transfer-Encoding',
	'Upgrade',
	'User-Agent',
	'Via'];

// 常见的自定义头部，可能导致 CORS 预检问题
var customHeaders = [
	'operatorsource',
	'x-requested-with',
	'x-custom-header',
	'authorization',
	'x-api-key',
	'x-auth-token',
	'x-csrf-token',
	'x-user-id',
	'x-session-id',
	'x-trace-id',
	'x-request-id'
];

// requestBatch 变量在 MV3 中不再需要

function handleHeader(headers) {
	if (!headers) return;
	var newHeaders = {}, headers = headers.split(/[\r\n]/).forEach(function (header) {
		var index = header.indexOf(":");
		var name = header.substr(0, index);
		var value = header.substr(index + 2);
		if (name) {
			newHeaders[name] = value;
		}

	})
	return newHeaders;
}

chrome.runtime.onMessage.addListener(function (request, _, cb) {
	if (request.action === 'get') {
		chrome.storage.local.get([request.name]).then((result) => {
			if (typeof cb === 'function') {
				cb(result[request.name] || null);
			}
		});
		return true; // 表示异步响应
	} else if (request.action === 'set') {
		chrome.storage.local.set({[request.name]: request.value}).then(() => {
			if (typeof cb === 'function') {
				cb();
			}
		});
		return true; // 表示异步响应
	}
})

async function sendAjax(req, successFn, errorFn) {
	try {
		req.headers = req.headers || {};
		req.headers['Content-Type'] = req.headers['Content-Type'] || req.headers['Content-type'] || req.headers['content-type'];// 兼容多种写法

		req.method = req.method || 'GET';
		req.headers = req.headers || {};

		// 处理请求数据
		var requestBody = null;
		if (req.method.toLowerCase() !== 'get' && req.method.toLowerCase() !== 'head' && req.method.toLowerCase() !== 'options') {
			if (!req.headers['Content-Type'] || req.headers['Content-Type'] == 'application/x-www-form-urlencoded') {
				req.headers['Content-Type'] = 'application/x-www-form-urlencoded';
				requestBody = formUrlencode(req.data);
			} else if (typeof req.data === 'object' && req.data) {
				requestBody = JSON.stringify(req.data);
			} else {
				requestBody = req.data;
			}
		} else {
			delete req.headers['Content-Type'];
		}

		// 处理查询参数
		if (req.query && typeof req.query === 'object') {
			var getUrl = formUrlencode(req.query);
			req.url = req.url + '?' + getUrl;
		}

		// 处理头部 - 在后台脚本中可以设置所有头部
		var finalHeaders = {};
		var unsafeHeaderArr = [];

		if (req.headers) {
			for (var name in req.headers) {
				var lowerName = name.toLowerCase();

				// 在后台脚本中，我们可以设置大部分头部
				// 只有真正不安全的头部才放入特殊处理
				if (unsafeHeader.indexOf(name) > -1) {
					unsafeHeaderArr.push({
						name: name,
						value: req.headers[name]
					});
				} else {
					// 后台脚本可以设置自定义头部，包括 operatorsource
					finalHeaders[name] = req.headers[name];
				}
			}

			// 如果有不安全的头部，编码后添加
			if (unsafeHeaderArr.length > 0) {
				finalHeaders['cross-request-unsafe-headers-list'] = encode(unsafeHeaderArr);
			}
		}

		// 移除可能导致 CORS 预检问题的标识头部
		// finalHeaders['cross-request-open-sign'] = '1';

		// 使用 fetch API 发送请求
		const controller = new AbortController();
		const timeoutId = setTimeout(() => controller.abort(), req.timeout || 1000000);

		const fetchOptions = {
			method: req.method,
			headers: finalHeaders,
			body: requestBody,
			signal: controller.signal
		};

		const response = await fetch(req.url, fetchOptions);
		clearTimeout(timeoutId);

		// 处理响应头
		var responseHeaders = {};
		response.headers.forEach((value, key) => {
			responseHeaders[key] = value;
		});

		// 处理不安全的响应头
		var finalResponseHeaders = responseHeaders;
		if (responseHeaders['cross-response-unsafe-headers-list']) {
			try {
				var unsafeResponseHeaders = decode(responseHeaders['cross-response-unsafe-headers-list']);
				delete responseHeaders['cross-response-unsafe-headers-list'];
				if (unsafeResponseHeaders && typeof unsafeResponseHeaders === 'object' && Object.keys(unsafeResponseHeaders).length > 0) {
					finalResponseHeaders = unsafeResponseHeaders;
				}
			} catch (e) {
				console.error('Failed to decode unsafe response headers:', e);
			}
		}

		const responseText = await response.text();

		const result = {
			headers: finalResponseHeaders,
			status: response.status,
			statusText: response.statusText,
			body: responseText
		};

		if (response.ok) {
			successFn(result);
		} else {
			errorFn(result);
		}

	} catch (error) {
		console.error('Fetch error:', error);
		errorFn({
			status: 500,
			statusText: 'Network Error',
			body: error.message || 'Request failed'
		});
	}
}

chrome.runtime.onConnect.addListener(function (port) {
	if (port.name === 'request') {
		port.onMessage.addListener(function (msg) {
			try {
				sendAjax(msg.req, function (res) {
					try {
						port.postMessage({
							id: msg.id,
							res: res
						});
					} catch (error) {
						console.error('Failed to send response:', error);
					}
				}, function (err) {
					try {
						port.postMessage({
							id: msg.id,
							res: err
						});
					} catch (error) {
						console.error('Failed to send error response:', error);
					}
				});
			} catch (error) {
				console.error('Failed to process request:', error);
				try {
					port.postMessage({
						id: msg.id,
						res: {
							status: 500,
							statusText: 'Internal Error',
							body: error.message
						}
					});
				} catch (postError) {
					console.error('Failed to send error message:', postError);
				}
			}
		});

		port.onDisconnect.addListener(function() {
			console.log('Port disconnected');
		});
	}
});

// ensureItem 函数在 MV3 中不再需要

// webRequest API 在 Manifest V3 中受到限制，移除相关代码
// 跨域请求功能将通过 background script 中的 XMLHttpRequest 来实现

// Service Worker 保活机制
chrome.runtime.onStartup.addListener(() => {
	console.log('Extension startup');
});

chrome.runtime.onInstalled.addListener(() => {
	console.log('Extension installed');
});

// 处理扩展图标点击事件
chrome.action.onClicked.addListener((tab) => {
	// 这里可以添加点击图标时的逻辑
	console.log('Extension icon clicked');
});


