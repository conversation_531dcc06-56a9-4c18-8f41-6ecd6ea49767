# Cross Request Chrome Extension - Manifest V3 安装指南

## 概述
此插件已成功升级到 Chrome Extension Manifest V3，可以在最新版本的 Chrome 浏览器中正常使用。

## 安装步骤

### 1. 开启开发者模式
1. 打开 Chrome 浏览器
2. 在地址栏输入 `chrome://extensions/`
3. 在右上角开启"开发者模式"

### 2. 加载插件
1. 点击"加载已解压的扩展程序"
2. 选择包含此插件文件的文件夹
3. 确认加载

### 3. 验证安装
1. 在浏览器工具栏中应该能看到插件图标
2. 点击图标应该能打开设置面板
3. 打开 `test_extension.html` 文件来测试插件功能

## 主要变更说明

### Manifest V3 升级内容
- ✅ 升级到 manifest_version 3
- ✅ 使用 Service Worker 替代后台页面
- ✅ 更新权限配置
- ✅ 使用 chrome.storage API 替代 localStorage
- ✅ 更新 API 调用方式

### 功能保持
- ✅ 跨域请求功能正常
- ✅ YApi 集成功能正常
- ✅ 用户界面保持不变
- ✅ 原有配置和使用方式不变

## 注意事项

1. **权限变更**: 插件现在使用 `host_permissions` 来处理跨域权限
2. **存储变更**: 配置数据现在存储在 Chrome 的扩展存储中，而不是 localStorage
3. **API 限制**: 由于 Manifest V3 的限制，某些高级网络拦截功能可能受到影响

## 故障排除

如果插件无法正常工作，请检查：
1. Chrome 版本是否为最新版本
2. 是否正确开启了开发者模式
3. 插件是否有必要的权限
4. 浏览器控制台是否有错误信息

## 测试
使用提供的 `test_extension.html` 文件来验证插件是否正常工作。
