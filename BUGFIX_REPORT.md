# Chrome Extension Manifest V3 Bug 修复报告

## 问题描述

在升级到 Manifest V3 后，插件出现以下错误：
1. `Failed to load resource: net::ERR_CERT_COMMON_NAME_INVALID`
2. `Error: Attempting to use a disconnected port object`
3. `XMLHttpRequest is not defined` (Service Worker 中)
4. `CORS policy: Request header field cross-request-open-sign is not allowed`
5. `Extension context invalidated` (扩展重新加载后)
6. `Cannot read properties of undefined (reading 'connect')`
7. YApi 接口调用失败

## 根本原因分析

### 1. Service Worker 连接管理问题
- Manifest V3 中 Service Worker 的生命周期与 V2 中的持久后台页面不同
- Service Worker 可能被浏览器暂停，导致 `chrome.runtime.connect` 连接断开
- 原代码没有处理连接断开和重连的情况

### 2. 错误处理不完善
- 当连接断开时，没有回退机制
- 缺少对网络错误的适当处理

### 3. Service Worker API 限制
- Service Worker 中不支持 XMLHttpRequest，需要使用 fetch API
- 需要重写所有网络请求逻辑

### 4. CORS 预检请求问题
- 自定义头部 `cross-request-open-sign` 触发 CORS 预检
- 预检请求被目标服务器拒绝
- 需要移除或优化自定义头部的使用

### 5. HTTPS 证书验证问题
- 某些 HTTPS 请求可能因为证书问题失败
- 需要提供备用的请求方式

## 修复方案

### 1. ✅ 连接管理优化
- 实现了 `createConnection()` 函数来管理连接生命周期
- 添加了连接断开检测和自动重连机制
- 在连接失败时自动重试

### 2. ✅ 错误处理增强
- 在 `sendAjaxByBack()` 中添加了 try-catch 错误处理
- 实现了从后台脚本到内容脚本的回退机制
- 添加了详细的错误日志

### 3. ✅ Service Worker 稳定性
- 添加了 Service Worker 保活机制
- 改进了消息传递的错误处理
- 添加了端口断开监听器

### 4. ✅ 请求策略优化
- 优先使用后台脚本发送请求（更好的跨域支持）
- 失败时自动回退到内容脚本请求
- 支持 HTTP 和 HTTPS 请求的不同处理策略

## 具体修改内容

### response.js 修改
```javascript
// 1. 连接管理
function createConnection() {
    // 创建新连接并设置错误处理
}

// 2. 错误处理
function sendAjaxByBack(id, req, successFn, errorFn) {
    // 添加连接检查和重试逻辑
}

// 3. 回退机制
// 后台请求失败时自动使用内容脚本请求
```

### background.js 修改
```javascript
// 1. 端口管理
chrome.runtime.onConnect.addListener(function (port) {
    // 添加错误处理和断开监听
});

// 2. Service Worker 保活
chrome.runtime.onStartup.addListener(() => {
    // 扩展启动处理
});
```

### manifest.json 修改
```json
{
    "permissions": [ "tabs", "storage", "activeTab" ]
}
```

## 测试验证

### 1. 连接稳定性测试
- 使用 `test_extension.html` 验证连接功能
- 测试连接断开后的自动重连

### 2. 请求功能测试
- HTTP 请求测试
- HTTPS 请求测试
- 错误处理测试

### 3. YApi 集成测试
- 验证与 YApi 平台的兼容性
- 测试跨域请求功能

## 使用说明

1. **重新加载插件**：在 Chrome 扩展管理页面重新加载插件
2. **测试功能**：打开 `test_extension.html` 进行功能测试
3. **YApi 使用**：在 YApi 平台中正常使用跨域请求功能

## 预期效果

- ✅ 解决连接断开错误
- ✅ 提高请求成功率
- ✅ 改善错误处理
- ✅ 保持与 YApi 的完全兼容性

修复后的插件应该能够稳定运行，并且在各种网络环境下都能正常处理跨域请求。
