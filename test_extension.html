<!DOCTYPE html>
<html>
<head>
    <title>Cross Request Extension Test</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>Cross Request Extension Test</h1>
    <div id="cross-request-sign" style="display: none;"></div>
    
    <button onclick="testCrossRequest()">Test Cross Request</button>
    <button onclick="checkConnection()">Check Connection</button>
    <div id="result"></div>

    <script>
        function testCrossRequest() {
            if (typeof crossRequest !== 'undefined') {
                document.getElementById('result').innerHTML = '<p style="color: green;">✓ crossRequest function is available</p>';

                // 测试一个简单的 HTTP 请求
                document.getElementById('result').innerHTML += '<p>Testing HTTP request...</p>';
                crossRequest({
                    url: 'http://httpbin.org/get',
                    method: 'GET',
                    success: function(res, header, data) {
                        document.getElementById('result').innerHTML += '<p style="color: green;">✓ HTTP Request successful</p>';
                        console.log('HTTP Success:', res);
                    },
                    error: function(error, header, data) {
                        document.getElementById('result').innerHTML += '<p style="color: orange;">⚠ HTTP Request failed: ' + (error.body || error.statusText || 'Unknown error') + '</p>';
                        console.log('HTTP Error:', error);
                    }
                });

                // 测试 HTTPS 请求
                setTimeout(function() {
                    document.getElementById('result').innerHTML += '<p>Testing HTTPS request...</p>';
                    crossRequest({
                        url: 'https://httpbin.org/get',
                        method: 'GET',
                        success: function(res, header, data) {
                            document.getElementById('result').innerHTML += '<p style="color: green;">✓ HTTPS Request successful</p>';
                            console.log('HTTPS Success:', res);
                        },
                        error: function(error, header, data) {
                            document.getElementById('result').innerHTML += '<p style="color: orange;">⚠ HTTPS Request failed: ' + (error.body || error.statusText || 'Unknown error') + '</p>';
                            console.log('HTTPS Error:', error);
                        }
                    });
                }, 2000);

            } else {
                document.getElementById('result').innerHTML = '<p style="color: red;">✗ crossRequest function is not available</p>';
            }
        }

        // 自动检测插件是否加载
        setTimeout(function() {
            if (typeof crossRequest !== 'undefined') {
                document.getElementById('result').innerHTML = '<p style="color: green;">✓ Extension loaded successfully</p>';
            } else {
                document.getElementById('result').innerHTML = '<p style="color: red;">✗ Extension not loaded</p>';
            }
        }, 1000);

        // 检查连接状态
        function checkConnection() {
            if (typeof crossRequest !== 'undefined') {
                document.getElementById('result').innerHTML += '<p>Checking connection status...</p>';
                // 简单的连接测试
                crossRequest({
                    url: 'http://httpbin.org/status/200',
                    method: 'GET',
                    timeout: 5000,
                    success: function(res, header, data) {
                        document.getElementById('result').innerHTML += '<p style="color: green;">✓ Connection test successful</p>';
                    },
                    error: function(error, header, data) {
                        document.getElementById('result').innerHTML += '<p style="color: red;">✗ Connection test failed: ' + (error.body || error.statusText || 'Unknown error') + '</p>';
                    }
                });
            }
        }
    </script>
</body>
</html>
